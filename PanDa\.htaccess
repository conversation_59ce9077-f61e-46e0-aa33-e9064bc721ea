# Laravel .htaccess Configuration
# Cấu hình Apache cho Laravel Framework

<IfModule mod_rewrite.c>
    # Bật URL Rewrite
    RewriteEngine On
    
    # Xử lý Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
    
    # Chuyển hướng tất cả request đến public folder
    RewriteCond %{REQUEST_URI} !^/public/
    RewriteRule ^(.*)$ /public/$1 [L]
</IfModule>

# Bảo mật - Ẩn các file nhạy cảm
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.json">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.lock">
    Order allow,deny
    Deny from all
</Files>

<Files "package.json">
    Order allow,deny
    Deny from all
</Files>

# Ẩn thư mục vendor và node_modules
<IfModule mod_rewrite.c>
    RewriteRule ^vendor/.*$ - [F,L]
    RewriteRule ^node_modules/.*$ - [F,L]
    RewriteRule ^storage/.*$ - [F,L]
    RewriteRule ^bootstrap/cache/.*$ - [F,L]
</IfModule>

# Cấu hình Cache cho Static Files
<IfModule mod_expires.c>
    ExpiresActive On
    
    # CSS và JS files
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # Images
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    
    # Fonts
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
</IfModule>

# Gzip Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE text/javascript
</IfModule>

# Security Headers
<IfModule mod_headers.c>
    # Ngăn chặn clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Ngăn chặn MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # XSS Protection
    Header set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Disable Server Signature
ServerSignature Off

# Custom Error Pages (tùy chọn)
# ErrorDocument 404 /404.html
# ErrorDocument 500 /500.html

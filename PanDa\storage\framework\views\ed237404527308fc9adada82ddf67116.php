<?php $__env->startSection('title', 'Giỏ Hàng - Hệ Thống H<PERSON>c Tập Online'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Giỏ Hàng Của Tôi</h4>
        <p class="text-muted mb-0">Quản Lý Các Khóa Học Bạn Muốn Mua</p>
      </div>
      <div>
        <span class="badge bg-primary fs-3"><?php echo e($cartItems->count()); ?> Kh<PERSON>a Họ<PERSON></span>
      </div>
    </div>
  </div>
</div>

<?php if($cartItems->count() > 0): ?>
<!-- Danh Sách Khóa Học Trong Giỏ -->
<div class="row">
  <div class="col-lg-8">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-code me-2"></i>Danh Sách Khóa Học
        </h5>
      </div>
      <div class="card-body p-0">
        <?php $__currentLoopData = $cartItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="border-bottom p-4">
          <div class="row align-items-center">
            <div class="col-md-3">
              <?php if($item->course->image): ?>
              <img src="<?php echo e(asset('storage/' . $item->course->image)); ?>" class="img-fluid rounded" alt="<?php echo e($item->course->title); ?>" style="height: 100px; object-fit: cover;">
              <?php else: ?>
              <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 100px;">
                <i class="fa fa-book fa-2x text-muted"></i>
              </div>
              <?php endif; ?>
            </div>
            
            <div class="col-md-6">
              <div class="mb-2">
                <span class="badge bg-primary"><?php echo e($item->course->category->name); ?></span>
                <span class="badge bg-secondary ms-1">
                  <?php if($item->course->level == 'beginner'): ?> Người Mới
                  <?php elseif($item->course->level == 'intermediate'): ?> Trung Cấp
                  <?php else: ?> Nâng Cao
                  <?php endif; ?>
                </span>
              </div>
              
              <h6 class="mb-2">
                <a href="<?php echo e(route('courses.show', $item->course->slug)); ?>" class="text-decoration-none">
                  <?php echo e($item->course->title); ?>

                </a>
              </h6>
              
              <p class="text-muted mb-2 small"><?php echo e(Str::limit($item->course->description, 100)); ?></p>
              
              <div class="text-muted small">
                <i class="fa fa-clock me-1"></i><?php echo e($item->course->duration_hours); ?> Giờ
                <i class="fa fa-play-circle ms-2 me-1"></i><?php echo e($item->course->total_lessons); ?> Bài
              </div>
            </div>
            
            <div class="col-md-3 text-end">
              <div class="mb-3">
                <h5 class="text-primary mb-0">
                  <?php if($item->course->price > 0): ?>
                  <?php echo e(number_format($item->course->price, 0, ',', '.')); ?>đ
                  <?php else: ?>
                  Miễn Phí
                  <?php endif; ?>
                </h5>
              </div>
              
              <div class="d-grid gap-2">
                <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#noteModal<?php echo e($item->id); ?>">
                  <i class="fa fa-edit me-1"></i>Ghi Chú
                </button>
                
                <form method="POST" action="<?php echo e(route('cart.destroy', $item->id)); ?>" class="d-inline">
                  <?php echo csrf_field(); ?>
                  <?php echo method_field('DELETE'); ?>
                  <button type="submit" class="btn btn-outline-danger btn-sm w-100" onclick="return confirm('Bạn Có Chắc Muốn Xóa?')">
                    <i class="fa fa-trash me-1"></i>Xóa
                  </button>
                </form>
              </div>
            </div>
          </div>
          
          <?php if($item->note): ?>
          <div class="row mt-3">
            <div class="col-12">
              <div class="alert alert-info mb-0">
                <strong>Ghi Chú:</strong> <?php echo e($item->note); ?>

              </div>
            </div>
          </div>
          <?php endif; ?>
        </div>

        <!-- Modal Ghi Chú -->
        <div class="modal fade" id="noteModal<?php echo e($item->id); ?>" tabindex="-1">
          <div class="modal-dialog">
            <div class="modal-content">
              <form method="POST" action="<?php echo e(route('cart.update', $item->id)); ?>">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <div class="modal-header">
                  <h5 class="modal-title">Ghi Chú Cho Khóa Học</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <div class="mb-3">
                    <label class="form-label">Khóa Học:</label>
                    <p class="fw-bold"><?php echo e($item->course->title); ?></p>
                  </div>
                  <div class="mb-3">
                    <label for="note<?php echo e($item->id); ?>" class="form-label">Ghi Chú</label>
                    <textarea class="form-control" id="note<?php echo e($item->id); ?>" name="note" rows="4" placeholder="Nhập Ghi Chú Của Bạn..."><?php echo e($item->note); ?></textarea>
                    <div class="form-text">Tối Đa 500 Ký Tự</div>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                  <button type="submit" class="btn btn-primary">
                    <i class="fa fa-save me-2"></i>Lưu Ghi Chú
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </div>
    </div>
  </div>

  <!-- Sidebar Tổng Kết -->
  <div class="col-lg-4">
    <div class="card sticky-top" style="top: 20px;">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fa fa-code me-2"></i>Tổng Kết Đơn Hàng
        </h5>
      </div>
      <div class="card-body">
        <div class="d-flex justify-content-between mb-3">
          <span>Số Lượng Khóa Học:</span>
          <span class="fw-bold"><?php echo e($cartItems->count()); ?></span>
        </div>
        
        <div class="d-flex justify-content-between mb-3">
          <span>Tạm Tính:</span>
          <span class="fw-bold"><?php echo e(number_format($total, 0, ',', '.')); ?>đ</span>
        </div>
        
        <hr>
        
        <div class="d-flex justify-content-between mb-4">
          <span class="h6">Tổng Cộng:</span>
          <span class="h5 text-primary fw-bold"><?php echo e(number_format($total, 0, ',', '.')); ?>đ</span>
        </div>
        
        <div class="d-grid gap-2">
          <button type="button" class="btn btn-primary btn-lg w-100" data-bs-toggle="modal" data-bs-target="#orderModal">
            <i class="fa fa-paper-plane me-2"></i>Gửi Yêu Cầu Mua Hàng
          </button>
          
          <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-outline-primary">
            <i class="fa fa-plus me-2"></i>Thêm Khóa Học Khác
          </a>
        </div>
        
        <hr>
        
        <div class="text-center">
          <h6>Lưu Ý:</h6>
          <ul class="list-unstyled text-muted small">
            <li><i class="fa fa-check text-success me-2"></i>Thanh Toán Thủ Công</li>
            <li><i class="fa fa-check text-success me-2"></i>Hỗ Trợ 24/7</li>
            <li><i class="fa fa-check text-success me-2"></i>Truy Cập Trọn Đời</li>
            <li><i class="fa fa-check text-success me-2"></i>Chứng Chỉ Hoàn Thành</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

<?php else: ?>
<!-- Giỏ Hàng Trống -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-body text-center py-5">
        <i class="fa fa-shopping-cart fa-4x text-muted mb-4"></i>
        <h4 class="text-muted mb-3">Giỏ Hàng Trống</h4>
        <p class="text-muted mb-4">Bạn Chưa Thêm Khóa Học Nào Vào Giỏ Hàng</p>
        <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-primary">
          <i class="fa fa-code me-2"></i>Khám Phá Khóa Học
        </a>
      </div>
    </div>
  </div>
</div>
<?php endif; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mt-4">
  <ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="<?php echo e(route('user.dashboard')); ?>">Trang Chủ</a></li>
    <li class="breadcrumb-item active" aria-current="page">Giỏ Hàng</li>
  </ol>
</nav>
<!-- Modal Gửi Yêu Cầu Mua Hàng -->
<div class="modal fade" id="orderModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fa fa-paper-plane me-2"></i>Gửi Yêu Cầu Mua Hàng
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>

      <form method="POST" action="<?php echo e(route('orders.store')); ?>">
        <?php echo csrf_field(); ?>
        <div class="modal-body">
          <div class="mb-4">
            <h6>Thông Tin Đơn Hàng</h6>
            <div class="table-responsive">
              <table class="table table-sm">
                <thead>
                  <tr>
                    <th>Khóa Học</th>
                    <th>Giá</th>
                  </tr>
                </thead>
                <tbody>
                  <?php $__currentLoopData = $cartItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <tr>
                    <td><?php echo e($item->course->title); ?></td>
                    <td class="fw-bold text-success">
                      <?php if($item->course->price > 0): ?>
                      <?php echo e(number_format($item->course->price, 0, ',', '.')); ?>đ
                      <?php else: ?>
                      Miễn Phí
                      <?php endif; ?>
                    </td>
                  </tr>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
                <tfoot>
                  <tr class="table-primary">
                    <th>Tổng Cộng</th>
                    <th class="text-success"><?php echo e(number_format($total, 0, ',', '.')); ?>đ</th>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>

          <div class="mb-3">
            <label for="payment_method" class="form-label">Phương Thức Thanh Toán <span class="text-danger">*</span></label>
            <select class="form-select <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="payment_method" name="payment_method" required>
              <option value="">Chọn Phương Thức</option>
              <option value="bank_transfer" <?php echo e(old('payment_method') == 'bank_transfer' ? 'selected' : ''); ?>>Chuyển Khoản Ngân Hàng</option>
              <option value="cash" <?php echo e(old('payment_method') == 'cash' ? 'selected' : ''); ?>>Tiền Mặt</option>
              <option value="other" <?php echo e(old('payment_method') == 'other' ? 'selected' : ''); ?>>Khác</option>
            </select>
            <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
          </div>

          <div class="mb-3">
            <label for="note" class="form-label">Ghi Chú</label>
            <textarea class="form-control <?php $__errorArgs = ['note'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="note" name="note" rows="4" placeholder="Nhập ghi chú về đơn hàng (tùy chọn)"><?php echo e(old('note')); ?></textarea>
            <?php $__errorArgs = ['note'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="invalid-feedback"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            <div class="form-text">Tối đa 1000 ký tự</div>
          </div>

          <div class="alert alert-info">
            <i class="fa fa-info-circle me-2"></i>
            <strong>Lưu ý:</strong> Sau khi gửi yêu cầu, admin sẽ xem xét và phản hồi trong vòng 24 giờ.
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="fa fa-times me-2"></i>Hủy
          </button>
          <button type="submit" class="btn btn-primary">
            <i class="fa fa-paper-plane me-2"></i>Gửi Yêu Cầu
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<?php if($errors->any()): ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
  var orderModal = new bootstrap.Modal(document.getElementById('orderModal'));
  orderModal.show();
});
</script>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Src Công Việc\PanDa\resources\views/cart/index.blade.php ENDPATH**/ ?>
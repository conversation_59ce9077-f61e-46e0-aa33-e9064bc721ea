

<?php $__env->startSection('title', 'Trang Chủ Học Viên'); ?>

<?php $__env->startSection('content'); ?>
<!-- Welcome Section -->
<div class="row mb-4">
  <div class="col-12">
    <div class="welcome-banner bg-gradient-primary text-white rounded-4 p-4">
      <div class="row align-items-center">
        <div class="col-md-8">
          <h2 class="mb-2"><PERSON><PERSON>à<PERSON>, <?php echo e(auth()->check() ? auth()->user()->name : 'Guest'); ?>! 🎓</h2>
          <p class="mb-0 opacity-75"><PERSON><PERSON><PERSON> Tr<PERSON> H<PERSON>c Tập Của Bạn. Bạn Đã Đ<PERSON> <?php echo e(auth()->check() ? auth()->user()->userCourses->count() : 0); ?> <PERSON><PERSON><PERSON><PERSON>!</p>
        </div>
        <div class="col-md-4 text-end">
          <div class="user-avatar">
            <?php if(auth()->user()->avatar): ?>
            <img src="<?php echo e(asset('storage/' . auth()->user()->avatar)); ?>" alt="<?php echo e(auth()->user()->name); ?>" class="rounded-circle" width="80" height="80">
            <?php else: ?>
            <div class="avatar-placeholder"><?php echo e(strtoupper(substr(auth()->user()->name, 0, 1))); ?></div>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Learning Stats -->
<div class="row mb-4">
  <div class="col-xl-3 col-md-6 mb-3">
    <div class="learning-stat-card bg-primary">
      <div class="stat-icon">
        <i class="fa fa-graduation-cap"></i>
      </div>
      <div class="stat-content">
        <h3><?php echo e(auth()->user()->userCourses->count()); ?></h3>
        <p>Khóa Học Đã Đăng Ký</p>
      </div>
    </div>
  </div>

  <div class="col-xl-3 col-md-6 mb-3">
    <div class="learning-stat-card bg-success">
      <div class="stat-icon">
        <i class="fa fa-check-circle"></i>
      </div>
      <div class="stat-content">
        <h3><?php echo e(auth()->user()->userCourses->where('completed', true)->count()); ?></h3>
        <p>Khóa Học Hoàn Thành</p>
      </div>
    </div>
  </div>

  <div class="col-xl-3 col-md-6 mb-3">
    <div class="learning-stat-card bg-warning">
      <div class="stat-icon">
        <i class="fa fa-clock"></i>
      </div>
      <div class="stat-content">
        <h3><?php echo e(auth()->user()->userCourses->where('completed', false)->count()); ?></h3>
        <p>Đang Học</p>
      </div>
    </div>
  </div>

  <div class="col-xl-3 col-md-6 mb-3">
    <div class="learning-stat-card bg-info">
      <div class="stat-icon">
        <i class="fa fa-certificate"></i>
      </div>
      <div class="stat-content">
        <h3><?php echo e(auth()->user()->userCourses->where('completed', true)->count()); ?></h3>
        <p>Chứng Chỉ</p>
      </div>
    </div>
  </div>
</div>

<!-- Continue Learning & Progress -->
<div class="row mb-4">
  <div class="col-xl-8 mb-4">
    <div class="card border-0 shadow-sm">
      <div class="card-header bg-transparent border-0 pb-0">
        <h5 class="card-title mb-0">
          <i class="fa fa-play-circle text-primary me-2"></i>Tiếp Tục Học Tập
        </h5>
      </div>
      <div class="card-body">
        <?php if(auth()->user()->userCourses->where('completed', false)->count() > 0): ?>
        <div class="row">
          <?php $__currentLoopData = auth()->user()->userCourses->where('completed', false)->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $userCourse): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <div class="col-md-4 mb-3">
            <div class="continue-course-card">
              <div class="course-thumbnail">
                <?php if($userCourse->course->thumbnail): ?>
                <img src="<?php echo e(asset('storage/' . $userCourse->course->thumbnail)); ?>" alt="<?php echo e($userCourse->course->title); ?>">
                <?php else: ?>
                <div class="thumbnail-placeholder">
                  <i class="fa fa-play-circle fa-3x"></i>
                </div>
                <?php endif; ?>
                <div class="course-progress">
                  <div class="progress">
                    <div class="progress-bar" style="width: <?php echo e($userCourse->progress ?? 0); ?>%"></div>
                  </div>
                  <small><?php echo e($userCourse->progress ?? 0); ?>% Hoàn Thành</small>
                </div>
              </div>
              <div class="course-info">
                <h6 class="mb-2"><?php echo e(Str::limit($userCourse->course->title, 40)); ?></h6>
                <p class="text-muted small mb-2"><?php echo e($userCourse->course->category->name); ?></p>
                <a href="<?php echo e(route('my-courses.show', $userCourse->course->id)); ?>" class="btn btn-primary btn-sm">
                  <i class="fa fa-play me-1"></i>Tiếp Tục
                </a>
              </div>
            </div>
          </div>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <?php else: ?>
        <div class="text-center py-4">
          <i class="fa fa-graduation-cap fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">Chưa Có Khóa Học Nào</h5>
          <p class="text-muted">Hãy Khám Phá Và Đăng Ký Khóa Học Đầu Tiên Của Bạn!</p>
          <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-primary">
            <i class="fa fa-search me-2"></i>Khám Phá Khóa Học
          </a>
        </div>
        <?php endif; ?>
      </div>
    </div>
  </div>

  <div class="col-xl-4 mb-4">
    <!-- Learning Progress -->
    <div class="card border-0 shadow-sm mb-4">
      <div class="card-header bg-transparent border-0 pb-0">
        <h5 class="card-title mb-0">
          <i class="fa fa-chart-pie text-success me-2"></i>Tiến Độ Học Tập
        </h5>
      </div>
      <div class="card-body text-center">
        <?php
          $totalCourses = auth()->user()->userCourses->count();
          $completedCourses = auth()->user()->userCourses->where('completed', true)->count();
          $completionRate = $totalCourses > 0 ? round(($completedCourses / $totalCourses) * 100) : 0;
        ?>
        <div class="progress-circle mb-3">
          <svg width="120" height="120">
            <circle cx="60" cy="60" r="50" stroke="#e9ecef" stroke-width="8" fill="none"></circle>
            <circle cx="60" cy="60" r="50" stroke="#28a745" stroke-width="8" fill="none"
                    stroke-dasharray="314" stroke-dashoffset="<?php echo e(314 - (314 * $completionRate / 100)); ?>"
                    stroke-linecap="round" transform="rotate(-90 60 60)"></circle>
          </svg>
          <div class="progress-text">
            <h3 class="mb-0 text-success"><?php echo e($completionRate); ?>%</h3>
            <small class="text-muted">Hoàn Thành</small>
          </div>
        </div>
        <p class="text-muted"><?php echo e($completedCourses); ?>/<?php echo e($totalCourses); ?> Khóa Học Đã Hoàn Thành</p>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="card border-0 shadow-sm">
      <div class="card-header bg-transparent border-0 pb-0">
        <h5 class="card-title mb-0">
          <i class="fa fa-bolt text-warning me-2"></i>Thao Tác Nhanh
        </h5>
      </div>
      <div class="card-body">
        <div class="d-grid gap-2">
          <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-primary">
            <i class="fa fa-search me-2"></i>Khám Phá Khóa Học
          </a>
          <a href="<?php echo e(route('user.profile.index')); ?>" class="btn btn-success">
            <i class="fa fa-user me-2"></i>Cập Nhật Hồ Sơ
          </a>
          <a href="<?php echo e(route('user.certificates.index')); ?>" class="btn btn-warning">
            <i class="fa fa-certificate me-2"></i>Chứng Chỉ Của Tôi
          </a>
          <a href="<?php echo e(route('user.courses.index')); ?>" class="btn btn-info">
            <i class="fa fa-book me-2"></i>Khóa Học Của Tôi
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Recent Activity & Achievements -->
<div class="row">
  <div class="col-xl-8 mb-4">
    <div class="card border-0 shadow-sm">
      <div class="card-header bg-transparent border-0 pb-0">
        <h5 class="card-title mb-0">
          <i class="fa fa-history text-info me-2"></i>Hoạt Động Gần Đây
        </h5>
      </div>
      <div class="card-body">
        <div class="activity-list">
          <?php if(auth()->user()->userCourses->count() > 0): ?>
          <?php $__currentLoopData = auth()->user()->userCourses->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $userCourse): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <div class="activity-item">
            <div class="activity-icon bg-primary">
              <i class="fa fa-graduation-cap"></i>
            </div>
            <div class="activity-content">
              <p class="mb-1">Đăng Ký Khóa Học <strong><?php echo e($userCourse->course->title); ?></strong></p>
              <small class="text-muted"><?php echo e($userCourse->created_at->diffForHumans()); ?></small>
            </div>
          </div>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          <?php else: ?>
          <div class="text-center py-3">
            <i class="fa fa-history fa-2x text-muted mb-2"></i>
            <p class="text-muted">Chưa Có Hoạt Động Nào</p>
          </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>

  <div class="col-xl-4 mb-4">
    <div class="card border-0 shadow-sm">
      <div class="card-header bg-transparent border-0 pb-0">
        <h5 class="card-title mb-0">
          <i class="fa fa-trophy text-warning me-2"></i>Thành Tựu
        </h5>
      </div>
      <div class="card-body">
        <div class="achievements">
          <?php if(auth()->user()->userCourses->count() >= 1): ?>
          <div class="achievement-item">
            <div class="achievement-icon bg-success">
              <i class="fa fa-star"></i>
            </div>
            <div class="achievement-info">
              <h6 class="mb-1">Người Học Mới</h6>
              <small class="text-muted">Đăng Ký Khóa Học Đầu Tiên</small>
            </div>
          </div>
          <?php endif; ?>

          <?php if(auth()->user()->userCourses->where('completed', true)->count() >= 1): ?>
          <div class="achievement-item">
            <div class="achievement-icon bg-warning">
              <i class="fa fa-medal"></i>
            </div>
            <div class="achievement-info">
              <h6 class="mb-1">Hoàn Thành Đầu Tiên</h6>
              <small class="text-muted">Hoàn Thành Khóa Học Đầu Tiên</small>
            </div>
          </div>
          <?php endif; ?>

          <?php if(auth()->user()->userCourses->count() >= 5): ?>
          <div class="achievement-item">
            <div class="achievement-icon bg-info">
              <i class="fa fa-fire"></i>
            </div>
            <div class="achievement-info">
              <h6 class="mb-1">Học Viên Tích Cực</h6>
              <small class="text-muted">Đăng Ký 5+ Khóa Học</small>
            </div>
          </div>
          <?php endif; ?>

          <?php if(auth()->user()->userCourses->count() == 0): ?>
          <div class="text-center py-3">
            <i class="fa fa-trophy fa-2x text-muted mb-2"></i>
            <p class="text-muted small">Chưa Có Thành Tựu Nào. Hãy Bắt Đầu Học Để Mở Khóa!</p>
          </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px !important;
}

.user-avatar .avatar-placeholder {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #fff, #f8f9fa);
  color: #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  margin-left: auto;
}

.learning-stat-card {
  background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark));
  border-radius: 20px;
  padding: 25px;
  color: white;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.learning-stat-card:hover {
  transform: translateY(-5px);
}

.learning-stat-card.bg-success {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.learning-stat-card.bg-warning {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.learning-stat-card.bg-info {
  background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 20px;
  font-size: 2.5rem;
  opacity: 0.3;
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-content p {
  margin-bottom: 10px;
  opacity: 0.9;
}

.continue-course-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.continue-course-card:hover {
  transform: translateY(-5px);
}

.course-thumbnail {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.course-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.course-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 8px;
}

.course-progress .progress {
  height: 4px;
  margin-bottom: 4px;
}

.course-info {
  padding: 15px;
}

.progress-circle {
  position: relative;
  display: inline-block;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.activity-list .activity-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-list .activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 15px;
}

.achievement-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.achievement-item:last-child {
  border-bottom: none;
}

.achievement-icon {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: 12px;
}

.card {
  border-radius: 15px;
}

.btn {
  border-radius: 10px;
  font-weight: 600;
}
</style>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Src Công Việc\PanDa\resources\views/user/dashboard.blade.php ENDPATH**/ ?>
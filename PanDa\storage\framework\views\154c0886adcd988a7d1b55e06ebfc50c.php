<?php $__env->startSection('title', 'Khóa H<PERSON>c Của Tôi'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
  <div class="col-12">
    <div class="d-flex align-items-center justify-content-between mb-4">
      <div>
        <h4 class="mb-0">Kh<PERSON><PERSON> H<PERSON> Của Tôi</h4>
        <p class="text-muted mb-0">Quản Lý Và Tiếp T<PERSON></p>
      </div>
      <div>
        <span class="badge bg-primary fs-3"><?php echo e($userCourses->total()); ?> Kh<PERSON><PERSON> Họ<PERSON></span>
      </div>
    </div>
  </div>
</div>

<!-- Bộ Lọc -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-body">
        <form method="GET" class="row g-3">
          <div class="col-md-3">
            <label class="form-label">Trạng Thái</label>
            <select name="status" class="form-select">
              <option value="">Tất Cả</option>
              <option value="not_started" <?php echo e(request('status') == 'not_started' ? 'selected' : ''); ?>>Chưa Bắt Đầu</option>
              <option value="in_progress" <?php echo e(request('status') == 'in_progress' ? 'selected' : ''); ?>>Đang Học</option>
              <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>Đã Hoàn Thành</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">Sắp Xếp</label>
            <select name="sort" class="form-select">
              <option value="recent" <?php echo e(request('sort') == 'recent' ? 'selected' : ''); ?>>Gần Đây Nhất</option>
              <option value="progress" <?php echo e(request('sort') == 'progress' ? 'selected' : ''); ?>>Tiến Độ Cao</option>
              <option value="name" <?php echo e(request('sort') == 'name' ? 'selected' : ''); ?>>Tên A-Z</option>
            </select>
          </div>
          <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-primary me-2">
              <i class="fa fa-filter me-1"></i>Lọc
            </button>
            <a href="<?php echo e(route('my-courses.index')); ?>" class="btn btn-outline-secondary">
              <i class="fa fa-refresh me-1"></i>Reset
            </a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<?php if($userCourses->count() > 0): ?>
<!-- Danh Sách Khóa Học -->
<div class="row">
  <?php $__currentLoopData = $userCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $userCourse): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
  <div class="col-lg-4 col-md-6 mb-4">
    <div class="card h-100 shadow-sm">
      <div class="card-header d-flex justify-content-between align-items-center">
        <span class="badge bg-<?php echo e($userCourse->status_class); ?>"><?php echo e($userCourse->status_text); ?></span>
        <small class="text-muted"><?php echo e($userCourse->course->category->name); ?></small>
      </div>
      
      <div class="card-body">
        <h5 class="card-title"><?php echo e($userCourse->course->title); ?></h5>
        <p class="card-text text-muted"><?php echo e(Str::limit($userCourse->course->description, 100)); ?></p>
        
        <!-- Tiến Độ -->
        <div class="mb-3">
          <div class="d-flex justify-content-between mb-1">
            <small>Tiến Độ</small>
            <small><?php echo e($userCourse->progress_percentage); ?>%</small>
          </div>
          <div class="progress" style="height: 8px;">
            <div class="progress-bar bg-success" style="width: <?php echo e($userCourse->progress_percentage); ?>%"></div>
          </div>
        </div>

        <!-- Thông Tin -->
        <div class="row text-center mb-3">
          <div class="col-4">
            <small class="text-muted d-block">Tổng Bài</small>
            <?php
              $totalLessons = $userCourse->course->chapters->sum(function($chapter) {
                return $chapter->lessons->count();
              });
            ?>
            <strong><?php echo e($totalLessons); ?></strong>
          </div>
          <div class="col-4">
            <small class="text-muted d-block">Đã Học</small>
            <strong><?php echo e(count($userCourse->completed_lessons ?? [])); ?></strong>
          </div>
          <div class="col-4">
            <small class="text-muted d-block">Thời Lượng</small>
            <strong><?php echo e($userCourse->course->duration_hours); ?>h</strong>
          </div>
        </div>

        <!-- Thời Gian -->
        <div class="mb-3">
          <?php if($userCourse->last_accessed_at): ?>
          <small class="text-muted">
            <i class="fa fa-clock me-1"></i>
            Học Lần Cuối: <?php echo e($userCourse->last_accessed_at->diffForHumans()); ?>

          </small>
          <?php endif; ?>
          <?php if($userCourse->enrolled_at): ?>
          <br><small class="text-muted">
            <i class="fa fa-calendar me-1"></i>
            Đăng Ký: <?php echo e($userCourse->enrolled_at->format('d/m/Y')); ?>

          </small>
          <?php endif; ?>
        </div>
      </div>

      <div class="card-footer bg-transparent">
        <div class="d-grid gap-2">
          <?php if(!$userCourse->started_at): ?>
          <?php
            $firstChapter = $userCourse->course->chapters->sortBy('position')->first();
            $firstLesson = $firstChapter ? $firstChapter->lessons->sortBy('position')->first() : null;
          ?>
          <?php if($firstLesson): ?>
          <a href="<?php echo e(route('user.courses.lessons.show', ['course' => $userCourse->course_id, 'chapter' => $firstLesson->chapter_id, 'lesson' => $firstLesson->id])); ?>" class="btn btn-success w-100">
            <i class="fa fa-play me-2"></i>Bắt Đầu Học
          </a>
          <?php else: ?>
          <button class="btn btn-secondary w-100" disabled>
            <i class="fa fa-exclamation-triangle me-2"></i>Chưa Có Bài Học
          </button>
          <?php endif; ?>
          <?php elseif($userCourse->completed_at): ?>
          <a href="<?php echo e(route('my-courses.show', $userCourse->course_id)); ?>" class="btn btn-primary">
            <i class="fa fa-certificate me-2"></i>Xem Chứng Chỉ
          </a>
          <?php else: ?>
          <?php
            $nextLesson = $userCourse->getNextLesson() ?? $userCourse->currentLesson;
            if (!$nextLesson && $userCourse->currentLesson) {
              $nextLesson = $userCourse->currentLesson;
            }
            if (!$nextLesson) {
              $firstChapter = $userCourse->course->chapters->sortBy('position')->first();
              $nextLesson = $firstChapter ? $firstChapter->lessons->sortBy('position')->first() : null;
            }
          ?>
          <?php if($nextLesson): ?>
          <a href="<?php echo e(route('user.courses.lessons.show', ['course' => $userCourse->course_id, 'chapter' => $nextLesson->chapter_id, 'lesson' => $nextLesson->id])); ?>" class="btn btn-warning w-100">
            <i class="fa fa-play me-2"></i>Tiếp Tục Học
          </a>
          <?php else: ?>
          <button class="btn btn-secondary w-100" disabled>
            <i class="fa fa-check-circle me-2"></i>Đã Hoàn Thành
          </button>
          <?php endif; ?>
          <?php endif; ?>

          <a href="<?php echo e(route('my-courses.show', $userCourse->course_id)); ?>" class="btn btn-outline-primary btn-sm">
            <i class="fa fa-eye me-1"></i>Xem Chi Tiết
          </a>
        </div>
      </div>
    </div>
  </div>
  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<!-- Phân Trang -->
<?php if($userCourses->hasPages()): ?>
<div class="row mt-4">
  <div class="col-12">
    <div class="d-flex justify-content-center">
      <?php echo e($userCourses->appends(request()->query())->links()); ?>

    </div>
  </div>
</div>
<?php endif; ?>

<?php else: ?>
<!-- Trống -->
<div class="row">
  <div class="col-12">
    <div class="text-center py-5">
      <i class="fa fa-graduation-cap fa-4x text-muted mb-4"></i>
      <h4 class="text-muted mb-3">Chưa Có Khóa Học Nào</h4>
      <p class="text-muted mb-4">Hãy Khám Phá Và Đăng Ký Các Khóa Học Thú Vị</p>
      <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-primary">
        <i class="fa fa-search me-2"></i>Khám Phá Khóa Học
      </a>
    </div>
  </div>
</div>
<?php endif; ?>

<!-- Thống Kê Nhanh -->
<div class="row mt-4">
  <div class="col-md-3">
    <div class="card bg-primary text-white">
      <div class="card-body text-center">
        <h4 class="mb-0"><?php echo e($userCourses->total()); ?></h4>
        <small>Tổng Khóa Học</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-warning text-white">
      <div class="card-body text-center">
        <h4 class="mb-0"><?php echo e($userCourses->where('started_at', '!=', null)->where('completed_at', null)->count()); ?></h4>
        <small>Đang Học</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-success text-white">
      <div class="card-body text-center">
        <h4 class="mb-0"><?php echo e($userCourses->where('completed_at', '!=', null)->count()); ?></h4>
        <small>Đã Hoàn Thành</small>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-info text-white">
      <div class="card-body text-center">
        <h4 class="mb-0"><?php echo e(round($userCourses->avg('progress_percentage') ?? 0)); ?>%</h4>
        <small>Tiến Độ Trung Bình</small>
      </div>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Src Công Việc\PanDa\resources\views/user/my-courses/index.blade.php ENDPATH**/ ?>